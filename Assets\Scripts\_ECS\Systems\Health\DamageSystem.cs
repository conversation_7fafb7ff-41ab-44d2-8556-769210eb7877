using Unity.Entities;
using PlayerFAP.Components;
using PlayerFAP.Systems.UI;
using GPUAnimationCrowds;
using MonoToECSShadow.Components;
using Unity.Jobs;
using UnityEngine;
using PlayerFAP.Managers;
using PlayerFAP.ScriptableObjects;
using Rukhanka.Test;
using Rukhanka.Toolbox;
using Unity.Burst;
using Unity.Mathematics;
using Unity.Transforms;
using Watermelon;
using Watermelon.SquadShooter;
using EnemyType = PlayerFAP.Authorings.EnemyType;
using PlayerFAP.DropResource;

namespace PlayerFAP.Systems.Health
{
// Define a custom ECB system to handle commands after DamageSystem and before HealthBarSpawnSystem
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(DamageSystem))]
    [UpdateBefore(typeof(HealthBarSpawnSystem))]
    public partial class DamageECBSystem : EntityCommandBufferSystem
    {
    }

    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(HealthBarSpawnSystem))]
    public partial class DamageSystem : SystemBase
    {
        private EntityCommandBuffer.ParallelWriter ecb;
        private DamageECBSystem commandBufferSystem;

        protected override void OnCreate()
        {
            // Use the custom ECB system
            RequireForUpdate<PlayerTransform>();
            commandBufferSystem = World.GetOrCreateSystemManaged<DamageECBSystem>();
        }

        // Can't use BurstCompile because we're using DebugLogManager.Instance.Log
        private partial struct DamageJob : IJobEntity
        {
            public EntityCommandBuffer.ParallelWriter ECB;
            public float DeathDissolveStartTime;
            public float DeathDissolveDuration;
            public float3 PlayerPos;
            [Unity.Collections.ReadOnly] public ComponentLookup<DeadTag> DeadTagLookup;
            [Unity.Collections.ReadOnly] public ComponentLookup<EnemyTypeComponent> EnemyTypeLookup;

            public void Execute(Entity entity, [EntityIndexInQuery] int sortKey,
                in DamageEvent damageEvent, ref HealthComponent health)
            {
                health.CurrentHealth -= damageEvent.DamageAmount;
                ECB.RemoveComponent<DamageEvent>(sortKey, entity);
                
                
                if (health.CurrentHealth <= 0)
                {
                    health.CurrentHealth = 0;

                    // Add DeadTag to mark entity as dead (if not already added)
                    if (!DeadTagLookup.HasComponent(entity))
                    {
                        // === Trigger drop system for enemy death ===
                        if (EnemyTypeLookup.HasComponent(entity))
                        {
                            var enemyTypeComp = EnemyTypeLookup[entity];

                            // Create drop request entity for DropPoolingSystem to process
                            var dropRequestEntity = ECB.CreateEntity(sortKey);
                            var dropRequest = new DropRequest
                            {
                                DeadEnemyEntity = entity,
                                DeathPosition = damageEvent.HitPosition,
                                EnemyType = (EnemyType)enemyTypeComp.Value, // Convert int to EnemyType enum
                                IsElite = false, // TODO: Add EliteTag component to properly detect elite enemies
                                DeathTime = 0f // Will be set by system
                            };
                            ECB.AddComponent(sortKey, dropRequestEntity, dropRequest);
                            ECB.SetComponentEnabled<DropRequest>(sortKey, dropRequestEntity, true);

                            // Debug logging
                            Debug.Log($"DamageSystem: Created DropRequest for enemy type {dropRequest.EnemyType} at position {dropRequest.DeathPosition}");
                        }

                        // === Trigger ragdoll on enemy root (mouth parent) ===
                        //Add ChestPosition from ChestColliderLookup
                        ECB.AddComponent<NeedsRagdollSwap>(sortKey, entity,
                            new NeedsRagdollSwap()
                            {
                                PlayerPosition = PlayerPos,
                                HitPosition = damageEvent.HitPosition
                            });

                        // Add UndetectableTag to prevent detection after death
                        ECB.AddComponent<UndetectableTag>(sortKey, entity);

                        // Remove DetectedTag if it exists
                        ECB.RemoveComponent<DetectedTag>(sortKey, entity);

                        // Add DeathTimerComponent to track time for dissolve effect

                        DebugLogManager.Instance.Log(
                            $"Entity {entity.Index} died. Adding DeadTag and DeathTimerComponent.");
                    }
                }
                else
                {
                    var hitTag = new HitTag
                    {
                        HitTime = 0f,
                        HitAnimationDuration = .5f // Adjust as needed
                    };
                    ECB.AddComponent<HitTag>(sortKey, entity, hitTag);
                    var vfxEntity = ECB.CreateEntity(sortKey);
                    ECB.AddComponent(sortKey, vfxEntity, new VFXTriggerEvent
                    {
                        TriggerType = VFXTriggerType.Hit,
                        Position = damageEvent.HitPosition,
                        Rotation = quaternion.identity,
                        Scale = .2f
                    });
                    
                    DebugLogManager.Instance.Log(
                        $"[DamageSystem] Added HitTag to entity {entity.Index} with duration {hitTag.HitAnimationDuration}",
                        DebugLogSettings.LogType.EnemyAnimation);
                }
            }

            /// <summary>
            /// Get default currency amount based on enemy type
            /// </summary>
            private static int GetDefaultCurrencyAmount(EnemyType enemyType)
            {
                switch (enemyType)
                {
                    case EnemyType.BossSniper:
                        return 15; // Boss enemies drop more
                    case EnemyType.Spider:
                        return 4; // Elite enemies
                    default:
                        return 2; // Regular enemies
                }
            }

            /// <summary>
            /// Get default weapon card drop chance based on enemy type
            /// </summary>
            private static float GetDefaultWeaponCardChance(EnemyType enemyType)
            {
                switch (enemyType)
                {
                    case EnemyType.BossSniper:
                        return 0.3f; // 30% for bosses
                    case EnemyType.Spider:
                        return 0.15f; // 15% for elite
                    default:
                        return 0.05f; // 5% for regular
                }
            }

            /// <summary>
            /// Get default weapon type based on enemy type
            /// </summary>
            private static WeaponSubModuleState GetDefaultWeaponType(EnemyType enemyType)
            {
                switch (enemyType)
                {
                    case EnemyType.BossSniper:
                        return WeaponSubModuleState.Mp4;
                    case EnemyType.Spider:
                        return WeaponSubModuleState.Pistol;
                    default:
                        return WeaponSubModuleState.Pistol;
                }
            }
        }
        
        [BurstCompile]
        protected override void OnUpdate()
        {
            ecb = commandBufferSystem.CreateCommandBuffer().AsParallelWriter();

            // Get configuration values
            var config = HealthSystemManager.Instance.Configuration;
            Entity playerEntity = SystemAPI.GetSingletonEntity<PlayerTransform>();
            float3 playerPos = SystemAPI.GetComponent<LocalTransform>(playerEntity).Position;

            var job = new DamageJob
            {
                ECB = ecb,
                PlayerPos = playerPos,
                DeathDissolveStartTime = config.deathDissolveStartTime,
                DeathDissolveDuration = config.deathDissolveDuration,
                DeadTagLookup = SystemAPI.GetComponentLookup<DeadTag>(true), // Read-only lookup
                EnemyTypeLookup = SystemAPI.GetComponentLookup<EnemyTypeComponent>(true) // Read-only lookup
            };

            // Schedule the job with dependency chaining
            JobHandle jobHandle = job.ScheduleParallel(Dependency);
            commandBufferSystem.AddJobHandleForProducer(jobHandle);
            Dependency = jobHandle;
        }
    }
}
