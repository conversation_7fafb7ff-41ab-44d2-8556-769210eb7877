using System.Collections.Generic;
using Unity.Burst;
using Unity.Entities;
using Unity.Collections;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine; // For Time, Debug
using GimmeDOTSGeometry;
using PlayerFAP.Components;
using PlayerFAP.Components.Detection;
using PlayerFAP.Components.Player;
using PlayerFAP.Components.Weapon;
using PlayerFAP.Tags; // For NativeSparseOctree
using PlayerFAP.Systems.Detection; // For TargetStabilityUtility
// UnityEngine is already imported, but added here for clarity with Bounds
// Unity.Entities is already imported (Entity, IComponentData)
// Unity.Mathematics is already imported (float3)

public struct UnifiedOctreeComponent : IComponentData
{
    public NativeSparseOctree<Entity> Tree; // NativeSparseOctree<Entity>
    public Bounds Bounds; // UnityEngine.Bounds
    public int MaxDepth;
    public int InitialCapacity;
    public bool IsInitialized;
}

public struct DetectedEnemyData
{
    public Entity Entity;
    public float3 Position;
    public float Distance;
    public float Angle;
    public bool IsInFOV;
    public float Score;
}

public struct DetectedEnemyComparer : IComparer<DetectedEnemyData>
{
    public int Compare(DetectedEnemyData x, DetectedEnemyData y)
    {
        return y.Score.CompareTo(x.Score); // Sort in descending order
    }
}

public struct UnAimingTargetEventTag : IComponentData
{
}
// Assuming these components are defined elsewhere or will be

[UpdateInGroup(typeof(SimulationSystemGroup))]
public partial class OptimizedDetectionSystemOctree : SystemBase
{
    private EntityQuery playerQuery;
    private EntityQuery m_EnemyQuery; // For finding all potential enemies
    private EntityQuery detectionTargetQuery;

    // Queries for existing tags (will be kept)
    private EntityQuery inFOVTagQuery;
    //private EntityQuery currentTargetTagQuery;

    // Added for Spatial Hashing
    private EntityQuery m_SpatialPartitioningSingletonQuery;

    // Added for Octree
    private EntityQuery m_OctreePopulatorQuery;
    private EntityQuery m_OctreeSingletonQuery;


    protected override void OnCreate()
    {
        playerQuery = GetEntityQuery(
            new EntityQueryBuilder(Allocator.Temp)
                .WithAll<PlayerTag, SphereDetectSensorComponent, LocalToWorld, PlayerHeadTransformComponent>());

        m_EnemyQuery = GetEntityQuery( // Keep for finding enemies to process
            ComponentType.ReadOnly<EnemyTag>(),
            ComponentType.ReadOnly<LocalToWorld>(),
            ComponentType.Exclude<UndetectableTag>(),
            ComponentType.Exclude<DeadTag>()
        );

        inFOVTagQuery = GetEntityQuery(ComponentType.ReadOnly<InFOVTag>()); // Keep
        //currentTargetTagQuery = GetEntityQuery(ComponentType.ReadOnly<CurrentTargetTag>()); // Keep

        // ADD for Spatial Hashing
        m_SpatialPartitioningSingletonQuery =
            GetEntityQuery(ComponentType.ReadOnly<ProjectDawn.Navigation.AgentSpatialPartitioningSystem.Singleton>());

        // ADD INITIALIZATIONS FOR OCTREE QUERIES
        m_OctreePopulatorQuery = new EntityQueryBuilder(Allocator.Temp)
            .WithAll<EnemyTag, OctreeTargetTag, LocalToWorld>()
            .WithNone<DeadTag, UndetectableTag>()
            .Build(this);

        m_OctreeSingletonQuery = GetEntityQuery(ComponentType.ReadWrite<UnifiedOctreeComponent>());

        if (m_OctreeSingletonQuery.IsEmptyIgnoreFilter)
        {
            var octreeEntity = EntityManager.CreateEntity();
            EntityManager.AddComponentData(octreeEntity, new UnifiedOctreeComponent
            {
                IsInitialized = false,
                Bounds = new Bounds(float3.zero, new float3(2000f, 200f, 2000f)), // Default example bounds
                MaxDepth = 5,
                InitialCapacity = 512,
                Tree = default // Tree is not created here, just the component
            });
            // Debug.Log("OptimizedDetectionSystem: Created UnifiedOctreeComponent singleton entity in OnCreate.");
        }

        var entity = EntityManager.CreateEntity();
        EntityManager.AddComponentData(entity, new DetectionTargetComponent());
        EntityManager.AddBuffer<DetectedEnemyBuffer>(entity);
        detectionTargetQuery = GetEntityQuery(ComponentType.ReadWrite<DetectionTargetComponent>());

        //RequireForUpdate(playerQuery);
        //RequireForUpdate(m_SpatialPartitioningSingletonQuery); // ADD
        // Potentially add RequireForUpdate for m_OctreeSingletonQuery if system should not run without it
        // For now, following instructions strictly.
    }

    [BurstCompile]
    protected override void OnUpdate()
    {
        if (playerQuery.IsEmpty) return;

        float currentTime = (float)SystemAPI.Time.ElapsedTime; // Use SystemAPI for time in SystemBase
        var playerEntity = playerQuery.GetSingletonEntity();
        var sensor = SystemAPI.GetComponent<SphereDetectSensorComponent>(playerEntity);
        Entity detectionResultsEntity = SystemAPI.GetSingletonEntity<DetectionTargetComponent>(); 
        DetectionTargetComponent detectionTargetComponent = SystemAPI.GetComponent<DetectionTargetComponent>(detectionResultsEntity); 

        bool hasCurrentTarget = detectionTargetComponent.HasTarget;

        if ((currentTime - sensor.LastCheckTime) < sensor.CheckInterval && hasCurrentTarget)
        {
            Entity _currentTargetEntity = detectionTargetComponent.CurrentTarget; 
            // Ensure the target entity is still valid and has position data
            if ( EntityManager.Exists(_currentTargetEntity) && SystemAPI.HasComponent<LocalToWorld>(_currentTargetEntity))
            {
                float3 _currentTargetPosition = SystemAPI.GetComponent<LocalToWorld>(_currentTargetEntity).Position;
                float epsilon = 0.1f;
                if (math.all(math.abs(_currentTargetPosition - detectionTargetComponent.CurrentPosition) < epsilon))
                {
                    detectionTargetComponent.CurrentPosition = _currentTargetPosition;
                    EntityManager.SetComponentData(detectionResultsEntity, detectionTargetComponent);
                }
            }

            return; // IMPORTANT: Skip the full detection logic
        }
        
        // --- Code path for NO current target ---
        // Respect the check interval for a new full scan
        if (currentTime - sensor.LastCheckTime < sensor.CheckInterval)
        {
            return; // Interval hasn't passed, do nothing further
        }

        // Interval has passed for a new scan. Update LastCheckTime.
        var sensorCopyToUpdate = sensor; 
        sensorCopyToUpdate.LastCheckTime = currentTime;
        SystemAPI.SetComponent(playerEntity, sensorCopyToUpdate); 

        // ---- FULL DETECTION LOGIC PROCEEDS BELOW ----
        // These are needed for the full detection logic
        var detectionTargetEntity = SystemAPI.GetSingletonEntity<DetectionTargetComponent>();
        var playerTransform = SystemAPI.GetComponent<LocalToWorld>(playerEntity);

        // ---- START MODIFICATION ----
        // Clear previous DetectedTag and InFOVTag from enemies
        EntityManager.RemoveComponent<DetectedTag>(m_EnemyQuery); // Clears from all enemies matching m_EnemyQuery
        EntityManager.RemoveComponent<InFOVTag>(m_EnemyQuery); // Clears from all enemies matching m_EnemyQuery
        EntityManager.RemoveComponent<CurrentTargetTag>(m_EnemyQuery); // Clears from all enemies matching m_EnemyQuery
        // CurrentTargetTag is not explicitly managed by a cleanup query in the provided code, handle if necessary.

        // DetectedEnemyBuffer on detectionTargetEntity is now managed entirely by the ECB created later in this method.
        // var detectedEnemiesBuffer_direct_handle_removed; // Original 'var detectedEnemiesBuffer' is removed. Its operations (clear/add) are moved to ECB.

        // == NEW BROAD-PHASE SELECTION LOGIC START ==
        // Use Allocator.Temp since this list is used on main thread within a single frame.
        NativeList<Entity> nearbyEnemies = new NativeList<Entity>(Allocator.Temp);

        //if (sensor.Method == DetectionMethod.Octree)
        //{
        if (m_OctreeSingletonQuery.IsEmptyIgnoreFilter ||
            !m_OctreeSingletonQuery.TryGetSingletonEntity<UnifiedOctreeComponent>(out Entity octreeSingletonEntity))
        {
            //Debug.LogError("Octree method selected but UnifiedOctreeComponent singleton not found in OnUpdate.");
            if (nearbyEnemies.IsCreated) nearbyEnemies.Dispose();
            return; // Early exit if Octree setup is missing
        }

        // It's good practice to use SystemAPI for component access in SystemBase
        var octreeComp = SystemAPI.GetComponent<UnifiedOctreeComponent>(octreeSingletonEntity);

        // Dispose previous tree if it exists
        if (octreeComp.Tree.IsCreated)
        {
            octreeComp.Tree.Dispose();
        }

        // Using statement ensures entitiesToPopulate is disposed.
        using (var entitiesToPopulate = m_OctreePopulatorQuery.ToEntityArray(Allocator.TempJob))
        {
            int numEntities = entitiesToPopulate.Length;
            // Ensure capacity is at least 1 if numEntities is 0, as NativeSparseOctree might not like 0 capacity.
            int capacity = math.max(1, math.max(octreeComp.InitialCapacity, numEntities));

            // Tree must use Allocator.Persistent as it's stored in a component across frames.
            octreeComp.Tree = new NativeSparseOctree<Entity>(
                octreeComp.Bounds.center,
                octreeComp.Bounds.size,
                octreeComp.MaxDepth,
                capacity,
                Allocator.Persistent);

            // Get ReadOnly ComponentLookup for LocalToWorld
            var ltwLookup = SystemAPI.GetComponentLookup<LocalToWorld>(true);

            for (int i = 0; i < numEntities; i++)
            {
                var entityToInsert = entitiesToPopulate[i];
                // Check if entity still exists and has LocalToWorld (it should due to query filters)
                // EntityManager.Exists might be expensive here; query should guarantee presence.
                // Relying on ltwLookup.HasComponent for safety.
                if (ltwLookup.HasComponent(entityToInsert)) // EntityManager.Exists(entityToInsert) && 
                {
                    octreeComp.Tree.Insert(ltwLookup[entityToInsert].Position, entityToInsert);
                }
            }

            octreeComp.IsInitialized = numEntities > 0; // Set IsInitialized based on population
        } // entitiesToPopulate is disposed here.

        SystemAPI.SetComponent<UnifiedOctreeComponent>(octreeSingletonEntity,
            octreeComp); // Write back changes to the component

        // == ADD THIS OCTREE QUERY LOGIC ==
        if (octreeComp.IsInitialized && octreeComp.Tree.IsCreated && octreeComp.Tree.Count > 0)
        {
            // playerTransform is available from the top of OnUpdate(), use its Position for the query
            NativeList<uint> cellCodes = new NativeList<uint>(Allocator.TempJob);
            try
            {
                // Main thread completion for simplicity in this step. 
                // Consider jobification for performance optimization later.
                octreeComp.Tree.GetCellsInRadius(playerTransform.Position, sensor.DetectionRange, ref cellCodes)
                    .Complete();

                var dataBuckets = octreeComp.Tree.GetDataBuckets();
                var ltwLookup = SystemAPI.GetComponentLookup<LocalToWorld>(true); // ReadOnly

                for (int i = 0; i < cellCodes.Length; ++i)
                {
                    if (dataBuckets.TryGetFirstValue(cellCodes[i], out Entity item, out var iterator))
                    {
                        do
                        {
                            // Check entity exists and has transform, then perform precise distance check
                            // EntityManager.Exists might be too slow here, rely on HasComponent.
                            if (ltwLookup.HasComponent(item)) // EntityManager.Exists(item) && 
                            {
                                if (math.distancesq(playerTransform.Position, ltwLookup[item].Position) <=
                                    sensor.DetectionRange * sensor.DetectionRange)
                                {
                                    nearbyEnemies.Add(item);
                                }
                            }
                        } while (dataBuckets.TryGetNextValue(out item, ref iterator));
                    }
                }
            }
            finally
            {
                if (cellCodes.IsCreated) cellCodes.Dispose();
            }
            // Debug.Log($"Octree query found {nearbyEnemies.Length} potential entities.");
        }
        //}
        // else // sensor.Method == DetectionMethod.SpatialHashing
        // {
        //     if (m_SpatialPartitioningSingletonQuery.IsEmpty)
        //     {
        //         Debug.LogError("SpatialHashing selected but AgentSpatialPartitioningSystem.Singleton not found.");
        //         // nearbyEnemies is already created and will be disposed later, so just return.
        //         return; 
        //     }
        //     var spatialSystem = m_SpatialPartitioningSingletonQuery.GetSingleton<ProjectDawn.Navigation.AgentSpatialPartitioningSystem.Singleton>();
        //     
        //     // playerTransform is available from the top of OnUpdate()
        //     var queryProcessor = new NearbyEntityCollector
        //     {
        //         PlayerPosition = playerTransform.Position,
        //         DetectionRadiusSq = sensor.DetectionRadius * sensor.DetectionRadius,
        //         NearbyEnemies = nearbyEnemies, // This list will be populated by the query
        //         EntityManager = EntityManager 
        //     };
        //
        //     spatialSystem.QueryCircle(
        //         playerTransform.Position,
        //         sensor.DetectionRadius,
        //         ref queryProcessor,
        //         ProjectDawn.Navigation.NavigationLayers.Everything 
        //     );
        //     // Debug.Log($"Found {nearbyEnemies.Length} enemies via Spatial Hashing.");
        // }
        // == NEW BROAD-PHASE SELECTION LOGIC END ==
        
        // Continue to processing of 'nearbyEnemies' below

    // Process 'nearbyEnemies' list (populated by Spatial Hashing for now, or empty if Octree was chosen)
    // - Perform FOV checks
    // - Calculate scores
    // - Add DetectedTag / InFOVTag
    // - Populate detectedEnemiesBuffer
    // This logic would be similar to what was in the original OptimizedDetectionSystem after KDTree/fallback query
    var playerHeadTransform = playerQuery.GetSingleton<PlayerHeadTransformComponent>(); // Get player head transform

    // Step 1: Collect all detected enemies into a list for clustering/sorting
    var detectedEnemyList = new NativeList<DetectedEnemyData>(Allocator.Temp);
    // Main-thread structural changes are applied immediately via Playback below; use Temp and dispose same frame
    var ecb = new EntityCommandBuffer(Allocator.Temp);

    // Clear/prepare DetectedEnemyBuffer on detectionTargetEntity via ECB.
    // detectionTargetEntity is available from earlier in OnUpdate.
    ecb.SetBuffer<DetectedEnemyBuffer>(detectionTargetEntity);

    foreach (var enemyEntity in nearbyEnemies)
    {
        var enemyLTW = EntityManager.GetComponentData<LocalToWorld>(enemyEntity);
        float3 enemyPosition = enemyLTW.Position;
        float3 playerEyePosition = playerHeadTransform.Position;

        float3 toEnemy = enemyPosition - playerEyePosition;
        float distanceSq = math.lengthsq(toEnemy);
        float distance = math.sqrt(distanceSq);

        if (distanceSq <= sensor.DetectionRange * sensor.DetectionRange)
        {
            float3 playerForward = math.mul(playerHeadTransform.Rotation, math.forward());
            float3 toEnemyNormalized = math.normalize(toEnemy);

            float dotProduct = math.dot(playerForward, toEnemyNormalized);

            // Ensure dotProduct is within acos valid range [-1, 1] due to potential floating point inaccuracies
            dotProduct = math.clamp(dotProduct, -1f, 1f);

            float angleToEnemyRad = math.acos(dotProduct);
            float angleInDegrees = math.degrees(angleToEnemyRad);
            float sensorDetectionAngleRadHalf = math.radians(sensor.DetectionAngle / 2f);

            bool isInFOV = angleToEnemyRad <= sensorDetectionAngleRadHalf;

            // Scoring logic (same as classic system)
            float distanceScore = 1.0f - (distanceSq / (sensor.DetectionRange * sensor.DetectionRange));
            float angleScore = isInFOV ? (1.0f - (angleInDegrees / (sensor.DetectionAngle / 2.0f))) : 0f;
            float calculatedScore = (distanceScore * sensor.distanceWeight) + (angleScore * sensor.angleWeight);

            // Collect for clustering/sorting
            detectedEnemyList.Add(new DetectedEnemyData
            {
                Entity = enemyEntity,
                Position = enemyPosition,
                Distance = distance,
                Angle = angleInDegrees,
                IsInFOV = isInFOV,
                Score = calculatedScore
            });
        }
        // else: out of detection range, skip
    }

    // Step 2: Apply target stability logic before sorting
    // Get target stability components if they exist
    bool hasTargetStability = EntityManager.HasComponent<TargetStabilityComponent>(playerEntity);
    bool hasHysteresisTargeting = EntityManager.HasComponent<HysteresisTargeting>(playerEntity);
    bool hasSectorTargeting = EntityManager.HasComponent<SectorTargeting>(playerEntity);

    Entity selectedTarget = Entity.Null;

    if (detectedEnemyList.Length > 0)
    {
        if (hasTargetStability)
        {
            // Solution 1: Current Target Bias (Primary method)
            var targetStability = EntityManager.GetComponentData<TargetStabilityComponent>(playerEntity);

            // Apply current target bias to scoring
            TargetStabilityUtility.ApplyCurrentTargetBias(ref detectedEnemyList,
                targetStability.CurrentTarget, targetStability.CurrentTargetBias);

            // Select stable target
            selectedTarget = TargetStabilityUtility.SelectStableTarget(detectedEnemyList,
                ref targetStability, SystemAPI.Time.ElapsedTime);

            // Update component
            EntityManager.SetComponentData(playerEntity, targetStability);
        }
        else if (hasHysteresisTargeting)
        {
            // Solution 2: Hysteresis-Based Targeting (Alternative method)
            var hysteresisTargeting = EntityManager.GetComponentData<HysteresisTargeting>(playerEntity);
            selectedTarget = TargetStabilityUtility.SelectTargetWithHysteresis(detectedEnemyList,
                ref hysteresisTargeting);
            EntityManager.SetComponentData(playerEntity, hysteresisTargeting);
        }
        else if (hasSectorTargeting)
        {
            // Solution 4: Sector-Based Targeting (For circular formations)
            var sectorTargeting = EntityManager.GetComponentData<SectorTargeting>(playerEntity);
            selectedTarget = TargetStabilityUtility.SelectSectorBasedTarget(detectedEnemyList,
                ref sectorTargeting, SystemAPI.Time.ElapsedTime);
            EntityManager.SetComponentData(playerEntity, sectorTargeting);
        }
        else
        {
            // Fallback: Simple distance-based stability (Solution 5)
            var currentTarget = Entity.Null;
            var dtcCurrent = SystemAPI.GetComponent<DetectionTargetComponent>(detectionTargetEntity);
            if (dtcCurrent.HasTarget)
            {
                currentTarget = dtcCurrent.CurrentTarget;
            }

            selectedTarget = TargetStabilityUtility.SelectStableTargetSimple(detectedEnemyList,
                currentTarget, sensor.TargetSwitchDistanceThreshold, sensor.TargetSwitchScoreThreshold);
        }
    }

    // Step 3: Sort by score (descending) for buffer population
    detectedEnemyList.Sort(new DetectedEnemyComparer());

    // Step 4: Clear buffer and repopulate with sorted/clustered results
    for (int i = 0; i < detectedEnemyList.Length; i++)
    {
        var enemy = detectedEnemyList[i];
        ecb.AppendToBuffer(detectionTargetEntity, new DetectedEnemyBuffer
        {
            Entity = enemy.Entity,
            Position = enemy.Position,
            IsInFOV = enemy.IsInFOV,
            Score = enemy.Score
        });
        // Tag with ECB for DOTS safety
        ecb.AddComponent<DetectedTag>(enemy.Entity);
        if (enemy.IsInFOV)
            ecb.AddComponent<InFOVTag>(enemy.Entity);
    }
    
    //ecb.AddComponent<CurrentTargetTag>(currentTargetEntity);

    detectedEnemyList.Dispose();
    ecb.Playback(EntityManager); // Structural changes (AddComponent on enemies) are applied here
    ecb.Dispose();

    // ---- MODIFICATION: Re-acquire the buffer ----
    // Re-acquire the buffer AFTER playback to ensure it's valid,
    // as the original 'detectedEnemiesBuffer' instance (obtained via SystemAPI.GetBuffer earlier) is now potentially stale
    // due to structural changes triggered by ecb.Playback().
    // 'detectionTargetEntity' should still be valid from earlier in OnUpdate().
    DynamicBuffer<DetectedEnemyBuffer> currentDetectedEnemiesBuffer;

    // Re-fetch the singleton entity in case it was changed/recreated during ECB playback or by other systems.
    Entity freshDetectionTargetEntity;
    try
    {
        freshDetectionTargetEntity = SystemAPI.GetSingletonEntity<DetectionTargetComponent>();
    }
    catch (System.InvalidOperationException e) // Handles cases where singleton might not exist or multiple exist
    {
        Debug.LogError(
            $"[Targeting] Failed to get DetectionTargetComponent singleton after ECB playback: {e.Message}. Ensure the singleton is correctly managed.");
        // Ensure TempJob containers are disposed before early return
        if (nearbyEnemies.IsCreated) nearbyEnemies.Dispose();
        return; // Cannot proceed without the target entity.
    }

    // Get a fresh BufferLookup AFTER playback for read-only access.
    var postPlaybackBufferLookup =
        SystemAPI.GetBufferLookup<DetectedEnemyBuffer>(true); // true indicates isReadOnly

    if (postPlaybackBufferLookup.HasBuffer(freshDetectionTargetEntity))
    {
        currentDetectedEnemiesBuffer = postPlaybackBufferLookup[freshDetectionTargetEntity];
    }
    else
    {
        // This case implies the (freshly obtained) singleton entity does not have the expected buffer.
        Debug.LogError(
            $"[Targeting] Critical error: Fresh DetectionTargetEntity {freshDetectionTargetEntity} does not have DetectedEnemyBuffer after ECB playback. Ensure buffer is added in OnCreate and not removed.");
        if (nearbyEnemies.IsCreated) nearbyEnemies.Dispose();
        return; // Exit update if buffer is missing.
    }
    // ---- END MODIFICATION ----

    // The foreach loop for nearbyEnemies and its internal if block should be properly closed before this point.

// ---- NEW LOGIC TO BE ADDED START ---- (This comment was in your original code)

// The detectedEnemiesBuffer is on playerEntity and should be populated by the preceding foreach loop.
// playerEntity is available from the top of OnUpdate.
        Entity currentTargetEntity = Entity.Null;
        float3 currentTargetPosition = float3.zero;
        float currentTargetScore = 0f;
        bool hasTarget = false;

        bool isCurrentTargetInFOV = false;
        // Use the newly acquired, valid buffer: currentDetectedEnemiesBuffer
        if (currentDetectedEnemiesBuffer.Length > 0) // This was the error line, now using the valid buffer
        {
            hasTarget = true;

            // Use the selected target from our stability system
            if (selectedTarget != Entity.Null)
            {
                // Find the selected target in the buffer to get its data
                bool targetFound = false;
                for (int i = 0; i < currentDetectedEnemiesBuffer.Length; i++)
                {
                    var enemyData = currentDetectedEnemiesBuffer[i];
                    if (enemyData.Entity == selectedTarget)
                    {
                        currentTargetEntity = selectedTarget;
                        currentTargetPosition = enemyData.Position;
                        currentTargetScore = enemyData.Score;
                        isCurrentTargetInFOV = enemyData.IsInFOV;
                        targetFound = true;
                        break;
                    }
                }

                // Fallback: if selected target not found in buffer, use highest scoring target
                if (!targetFound && currentDetectedEnemiesBuffer.Length > 0)
                {
                    var bestEnemy = currentDetectedEnemiesBuffer[0]; // Buffer is sorted by score
                    currentTargetEntity = bestEnemy.Entity;
                    currentTargetPosition = bestEnemy.Position;
                    currentTargetScore = bestEnemy.Score;
                    isCurrentTargetInFOV = bestEnemy.IsInFOV;

                    Debug.LogWarning($"[TargetStability] Selected target {selectedTarget} not found in buffer, using fallback target {currentTargetEntity}");
                }
            }
            else
            {
                // No target selected by stability system, use highest scoring
                if (currentDetectedEnemiesBuffer.Length > 0)
                {
                    var bestEnemy = currentDetectedEnemiesBuffer[0]; // Buffer is sorted by score
                    currentTargetEntity = bestEnemy.Entity;
                    currentTargetPosition = bestEnemy.Position;
                    currentTargetScore = bestEnemy.Score;
                    isCurrentTargetInFOV = bestEnemy.IsInFOV;
                }
            }


// Update DetectionTargetComponent on its singleton
// Assuming DetectionTargetComponent is defined elsewhere and a singleton entity with it exists.
            var dtc = SystemAPI.GetComponent<DetectionTargetComponent>(
                detectionTargetEntity); // Get current state
            dtc.CurrentTarget = currentTargetEntity;
            dtc.CurrentPosition = currentTargetPosition;
            dtc.HasTarget = hasTarget;
            dtc.IsInFOV = isCurrentTargetInFOV;
            dtc.IsDetected = currentDetectedEnemiesBuffer.Length > 0; // IsDetected is true if any enemy is in buffer
            dtc.Score = hasTarget ? currentTargetScore : 0f;
            if (hasTarget)
            {
                dtc.LastKnownPosition = currentTargetPosition;
            }

            // else, LastKnownPosition remains from previous frame if no current target
            SystemAPI.SetComponent<DetectionTargetComponent>(detectionTargetEntity,
                dtc); // Set modified state

// Manage UnAimingTargetEventTag on player entity
// Assuming UnAimingTargetEventTag is defined elsewhere.
            bool shouldUnAim = !hasTarget;
            bool hasUnAimTag = EntityManager.HasComponent<UnAimingTargetEventTag>(playerEntity);

            if (shouldUnAim && !hasUnAimTag)
            {
                EntityManager.AddComponent<UnAimingTargetEventTag>(playerEntity);
                // Debug.Log("[AIM] No detected enemies. Transitioning to UN-AIM state.");
            }
            else if (!shouldUnAim && hasUnAimTag)
            {
                EntityManager.RemoveComponent<UnAimingTargetEventTag>(playerEntity);
                // Debug.Log("[AIM] Enemy detected. Transitioning to AIM state.");
            }
// ---- NEW LOGIC TO BE ADDED END ----

            if (nearbyEnemies.IsCreated) nearbyEnemies.Dispose(); // Ensure this is after the new logic
// ---- END MODIFICATION ----
        }
    }

    protected override void OnDestroy()
    {
        // Add Octree Disposal Logic:
        // Check if World and EntityManager are still valid, as OnDestroy can be called at various stages.
        // m_OctreeSingletonQuery should exist if OnCreate ran, but good to be safe.
        if (m_OctreeSingletonQuery != null && World != null && World.IsCreated)
        {
            // Check if the query itself found anything and didn't get destroyed somehow.
            if (!m_OctreeSingletonQuery.IsEmptyIgnoreFilter)
            {
                // Use TryGetSingletonEntity for safety.
                if (m_OctreeSingletonQuery.TryGetSingletonEntity<UnifiedOctreeComponent>(
                        out Entity octreeSingletonEntity))
                {
                    // Final check if the component still exists on the entity before trying to get it.
                    if (EntityManager.HasComponent<UnifiedOctreeComponent>(octreeSingletonEntity))
                    {
                        var unifiedOctreeComponent =
                            EntityManager.GetComponentData<UnifiedOctreeComponent>(octreeSingletonEntity);
                        if (unifiedOctreeComponent.Tree.IsCreated)
                        {
                            unifiedOctreeComponent.Tree.Dispose();
                            // Debug.Log("OptimizedDetectionSystem: Disposed Octree in OnDestroy.");
                        }
                    }
                }
            }
        }

        base.OnDestroy();
    }

// Helper struct for collecting entities from spatial query
    [BurstCompile]
    private struct NearbyEntityCollector : ProjectDawn.Navigation.ISpatialQueryEntity
    {
        public float3 PlayerPosition;
        public float DetectionRadiusSq;
        public NativeList<Entity> NearbyEnemies;
        [ReadOnly] public EntityManager EntityManager; // If needed to access components of found entity

        public void Execute(Entity entity, ProjectDawn.Navigation.AgentBody body,
            ProjectDawn.Navigation.AgentShape shape,
            LocalTransform transform)
        {
            // Basic distance check, more checks (e.g. FOV) would happen after this collection phase
            if (math.distancesq(PlayerPosition, transform.Position) <= DetectionRadiusSq)
            {
                // Check if it's an enemy (if spatial system doesn't already filter by EnemyTag)
                // This check might be redundant if the spatial partitioning system is only populated with enemies.
                // However, if it contains other agent types, a check here is good.
                // For this example, we assume the entities in spatial system are potential targets.
                NearbyEnemies.Add(entity);
            }
        }
    }
}