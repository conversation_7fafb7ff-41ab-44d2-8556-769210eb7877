using System;
using Unity.Entities;
using Unity.Mathematics;

namespace Rukhanka.Test.Pooling
{
    /// <summary>
    /// Utility class for easy pooling operations using any enum type
    /// </summary>
    public static class PoolingUtility
    {
        /// <summary>
        /// Request to spawn an entity from the pool using any enum
        /// </summary>
        public static void RequestSpawn(EntityManager entityManager, Entity poolManagerEntity, 
            Enum spawnTag, float3 position, quaternion rotation = default, float scale = 1f, Entity requestingEntity = default)
        {
            if (rotation.Equals(default))
                rotation = quaternion.identity;

            uint typeHash = GetEnumHash(spawnTag);
            
            var spawnBuffer = entityManager.GetBuffer<PoolSpawnRequest>(poolManagerEntity);
            spawnBuffer.Add(new PoolSpawnRequest
            {
                TypeHash = typeHash,
                Position = position,
                Rotation = rotation,
                Scale = scale,
                RequestingEntity = requestingEntity
            });
        }

        /// <summary>
        /// Request to spawn an entity from the pool using specific enum type
        /// </summary>
        public static void RequestSpawn<TEnum>(EntityManager entityManager, Entity poolManagerEntity, 
            TEnum spawnTag, float3 position, quaternion rotation = default, float scale = 1f, Entity requestingEntity = default)
            where TEnum : struct, Enum
        {
            if (rotation.Equals(default))
                rotation = quaternion.identity;

            uint typeHash = GetEnumHash(spawnTag);
            
            var spawnBuffer = entityManager.GetBuffer<PoolSpawnRequest>(poolManagerEntity);
            spawnBuffer.Add(new PoolSpawnRequest
            {
                TypeHash = typeHash,
                Position = position,
                Rotation = rotation,
                Scale = scale,
                RequestingEntity = requestingEntity
            });
        }

        /// <summary>
        /// Request to return an entity to the pool
        /// </summary>
        public static void RequestReturn(EntityManager entityManager, Entity poolManagerEntity, Entity entityToReturn)
        {
            var returnBuffer = entityManager.GetBuffer<PoolReturnRequest>(poolManagerEntity);
            returnBuffer.Add(new PoolReturnRequest
            {
                EntityToReturn = entityToReturn
            });
        }

        /// <summary>
        /// Get the pool manager entity
        /// </summary>
        public static Entity GetPoolManager(EntityManager entityManager)
        {
            var query = entityManager.CreateEntityQuery(typeof(PoolManagerTag));
            if (query.CalculateEntityCount() > 0)
            {
                return query.GetSingletonEntity();
            }
            return Entity.Null;
        }

        /// <summary>
        /// Check if an enum value is available in the pool
        /// </summary>
        public static bool IsEnumAvailable(EntityManager entityManager, Entity poolManagerEntity, Enum enumValue)
        {
            if (poolManagerEntity == Entity.Null) return false;
            
            uint typeHash = GetEnumHash(enumValue);
            var poolEntries = entityManager.GetBuffer<PoolEntry>(poolManagerEntity);
            foreach (var entry in poolEntries)
            {
                if (entry.TypeHash == typeHash)
                    return true;
            }
            return false;
        }

        /// <summary>
        /// Get hash for any enum value (consistent with PoolDatabase)
        /// </summary>
        public static uint GetEnumHash(Enum enumValue)
        {
            // Use string representation like PoolDatabase for consistency
            var typeName = enumValue.GetType().FullName;
            var valueName = enumValue.ToString();
            var typeHash = typeName.GetHashCode();
            var valueHash = valueName.GetHashCode();
            
            #if UNITY_EDITOR
            //UnityEngine.Debug.Log($"PoolingUtility.GetEnumHash: Type={typeName}, Value={valueName}, TypeHash={typeHash}, ValueHash={valueHash}, Result={(uint)(typeHash ^ valueHash)}");
            #endif
            
            return (uint)(typeHash ^ valueHash);
        }

        /// <summary>
        /// Get hash for specific enum type and value (consistent with PoolDatabase)
        /// </summary>
        public static uint GetEnumHash<TEnum>(TEnum enumValue) where TEnum : struct, Enum
        {
            // Use string representation like PoolDatabase for consistency
            var typeName = typeof(TEnum).FullName;
            var valueName = enumValue.ToString();
            var typeHash = typeName.GetHashCode();
            var valueHash = valueName.GetHashCode();
            
            #if UNITY_EDITOR
            //UnityEngine.Debug.Log($"PoolingUtility.GetEnumHash<T>: Type={typeName}, Value={valueName}, TypeHash={typeHash}, ValueHash={valueHash}, Result={(uint)(typeHash ^ valueHash)}");
            #endif
            
            return (uint)(typeHash ^ valueHash);
        }
        
        
    }
}

