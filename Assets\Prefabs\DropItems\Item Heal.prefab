%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &823147424966694468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 11180471435824382}
  - component: {fileID: 4001557125306614619}
  - component: {fileID: 1183483843081238417}
  m_Layer: 0
  m_Name: heal_plus
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &11180471435824382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 823147424966694468}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.1, z: 0}
  m_LocalScale: {x: 0.23026446, y: 0.23026446, z: 0.23026446}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4260683951245510383}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4001557125306614619
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 823147424966694468}
  m_Mesh: {fileID: -1264635489282502414, guid: cb4ebd0dd5f80204ea5779455dca6904, type: 3}
--- !u!23 &1183483843081238417
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 823147424966694468}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3658322aa152d704782646fab03ab41d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3487072023843955884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5462167876475760430}
  - component: {fileID: 8691775329129838247}
  m_Layer: 0
  m_Name: Trail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5462167876475760430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3487072023843955884}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.8, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1087613981833708450}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!96 &8691775329129838247
TrailRenderer:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3487072023843955884}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 150aee38b5d848b42b75593618992bb7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Time: 0.3
  m_PreviewTimeScale: 1
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.625
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: -2.5950909
        outSlope: -2.5950909
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.12689075
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: -0.30287096
        outSlope: -0.30287096
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.21344537
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0.5176471}
      key1: {r: 1, g: 1, b: 1, a: 0.43137255}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: 0
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MinVertexDistance: 0.1
  m_MaskInteraction: 0
  m_Autodestruct: 0
  m_Emitting: 1
  m_ApplyActiveColorSpace: 1
--- !u!1 &7619653228899683269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1087613981833708450}
  - component: {fileID: 4212937334072293355}
  - component: {fileID: 8363280718940023150}
  - component: {fileID: 9117795575956390239}
  - component: {fileID: 2877169769038758262}
  - component: {fileID: 1121498137143693060}
  m_Layer: 13
  m_Name: Item Heal
  m_TagString: Item
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1087613981833708450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5462167876475760430}
  - {fileID: 4260683951245510383}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &4212937334072293355
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: d65f8edf89b1c874680ed7957b64c11a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &8363280718940023150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 54298c8514c7c224ebc35edfc783e7df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DropType: 2
  CurrencyType: 0
  WeaponType: 0
  PoolTypeHash: 2
  PoolSize: 50
  VisualPrefab: {fileID: 0}
  PickupRadius: 1
  AutoPickupDelay: 10
  ScaleAnimationDuration: 0.5
  MovementAnimationDuration: 0.3
  ScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  MovementCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  BounceHeight: 0.2
  BounceSpeed: 2
  LifeTime: 30
  PickupSound: {fileID: 0}
  SpawnSound: {fileID: 0}
  PickupEffect: {fileID: 0}
  SpawnEffect: {fileID: 0}
--- !u!114 &9117795575956390239
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2fb757bd46edfe349a52b4ea886d67e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  rigConfigSource: 0
  avatar: {fileID: 0}
  avatarOptimizationMask: {fileID: 0}
  applyRootMotion: 0
  animationCulling: 0
  boneEntityStrippingMode: 0
  rootMotionMode: 0
  boneStrippingMask: {fileID: 0}
  hasAnimationEvents: 0
  hasAnimatorControllerEvents: 0
  animationEngine: 0
--- !u!114 &2877169769038758262
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b275e5f92732148048d7b77e264ac30e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShapeType: 0
  m_PrimitiveCenter:
    x: 0
    y: 0
    z: 0.000000018626451
  m_PrimitiveSize:
    x: 2
    y: 2
    z: 2
  m_PrimitiveOrientation:
    Value:
      x: -0
      y: 0
      z: 0
    RotationOrder: 4
  m_Capsule:
    Height: 2
    Radius: 1
    Axis: 2
  m_Cylinder:
    Height: 2
    Radius: 1
    Axis: 2
  m_CylinderSideCount: 20
  m_SphereRadius: 1
  m_MinimumSkinnedVertexWeight: 0.1
  m_ConvexHullGenerationParameters:
    m_SimplificationTolerance: 0.01
    m_BevelRadius: 0.05
    m_MinimumAngle: 2.5000002
  m_CustomMesh: {fileID: 0}
  m_ForceUnique: 0
  m_Material:
    m_SupportsTemplate: 1
    m_Template: {fileID: 0}
    m_CollisionResponse:
      m_Override: 0
      m_Value: 0
    m_Friction:
      m_Override: 0
      m_Value:
        Value: 0.5
        CombineMode: 0
    m_Restitution:
      m_Override: 0
      m_Value:
        Value: 0
        CombineMode: 2
    m_BelongsToCategories:
      m_Override: 0
      m_Value:
        Category00: 1
        Category01: 1
        Category02: 1
        Category03: 1
        Category04: 1
        Category05: 1
        Category06: 1
        Category07: 1
        Category08: 1
        Category09: 1
        Category10: 1
        Category11: 1
        Category12: 1
        Category13: 1
        Category14: 1
        Category15: 1
        Category16: 1
        Category17: 1
        Category18: 1
        Category19: 1
        Category20: 1
        Category21: 1
        Category22: 1
        Category23: 1
        Category24: 1
        Category25: 1
        Category26: 1
        Category27: 1
        Category28: 1
        Category29: 1
        Category30: 1
        Category31: 1
    m_CollidesWithCategories:
      m_Override: 0
      m_Value:
        Category00: 0
        Category01: 0
        Category02: 0
        Category03: 0
        Category04: 0
        Category05: 1
        Category06: 0
        Category07: 0
        Category08: 0
        Category09: 0
        Category10: 0
        Category11: 0
        Category12: 0
        Category13: 0
        Category14: 0
        Category15: 0
        Category16: 0
        Category17: 0
        Category18: 0
        Category19: 0
        Category20: 0
        Category21: 0
        Category22: 0
        Category23: 0
        Category24: 0
        Category25: 0
        Category26: 0
        Category27: 0
        Category28: 0
        Category29: 0
        Category30: 0
        Category31: 0
    m_CustomMaterialTags:
      m_Override: 0
      m_Value:
        Tag00: 0
        Tag01: 0
        Tag02: 0
        Tag03: 0
        Tag04: 0
        Tag05: 0
        Tag06: 0
        Tag07: 0
    m_SerializedVersion: 1
  m_SerializedVersion: 1
--- !u!114 &1121498137143693060
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7619653228899683269}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccea9ea98e38942e0b0938c27ed1903e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MotionType: 1
  m_Smoothing: 0
  m_Mass: 1
  m_LinearDamping: 0.01
  m_AngularDamping: 0.05
  m_InitialLinearVelocity:
    x: 0
    y: 0
    z: 0
  m_InitialAngularVelocity:
    x: 0
    y: 0
    z: 0
  m_GravityFactor: 1
  m_OverrideDefaultMassDistribution: 0
  m_CenterOfMass:
    x: 0
    y: 0
    z: 0
  m_Orientation:
    Value:
      x: 0
      y: 0
      z: 0
    RotationOrder: 4
  m_InertiaTensor:
    x: 0.4
    y: 0.4
    z: 0.4
  m_WorldIndex: 0
  m_CustomTags:
    Tag00: 0
    Tag01: 0
    Tag02: 0
    Tag03: 0
    Tag04: 0
    Tag05: 0
    Tag06: 0
    Tag07: 0
--- !u!1 &8451000283577639413
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4260683951245510383}
  m_Layer: 0
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4260683951245510383
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8451000283577639413}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.92, z: 0}
  m_LocalScale: {x: 4, y: 4, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 11180471435824382}
  m_Father: {fileID: 1087613981833708450}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
