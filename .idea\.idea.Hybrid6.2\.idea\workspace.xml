<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e6f24aa7-cf7a-424a-9feb-2bf20d415435" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../../../" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;RezaRahimi2&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Papen-Game-Studio/HybridCasualTempleClean.git&quot;,
    &quot;accountId&quot;: &quot;edb8ebee-4f5c-4785-a51e-9b1de79c72fb&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/51599aac63954963bc1e41e0384aad0b49a00/3d/********/02000076pdb39.il" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/ILViewer/9e0bacc3793b426a97191b85dc0dd95349c00/a0/12ff1509/02000076pdb39.il" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/1bb87569f5e345bca3d7d94e7c3eefa9b2200/99/2a0ae1bd/PhysicsGraphicalInterpolationBuffer.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/318e2b825f864e1f9076755f968808cd1eac00/66/********/Assert.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/318e2b825f864e1f9076755f968808cd1eac00/e0/2bacca8a/BatchMaterialID.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/34965f9dd2524b9fabed0cb9ab9483b99e4c00/63/cc1e75dd/GameObjectInspector.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/34965f9dd2524b9fabed0cb9ab9483b99e4c00/69/ee7693c8/Editor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/94360c27d2654bfeb7fbc79fb75bbcc3298e00/31/8c3a3f1e/Pooled.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a24ec2e12154426e8f9790758a2639a5182400/6d/a37608b8/IEnableableComponent.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d0bfc79f0a254c81bd3b7789bb5cc5eb29f600/da/6e8a610e/PoolEntry.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d6e80b37d98246a5859a00c653f9fdf346ae00/b6/078c9331/Array.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d6e80b37d98246a5859a00c653f9fdf346ae00/bb/1cb3c5c6/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/d6e80b37d98246a5859a00c653f9fdf346ae00/e3/0c91bb57/Buffer.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/db8de6bcb09e4773a9c6a44fcae2a8b529ac00/1d/79fc6fb4/OriginalState.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../Backup/15ImplemeentDetectionIndicatorAddPlayerHealthBar/Assets/Scripts/_ECS/Systems/Spawner/SpawnerSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../ECS1.0/ConvertShaderToRukhankaSupport/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Stubs/Unity/Debug.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../ECS1.0/ConvertShaderToRukhankaSupport/Packages/com.projectdawn.navigation.crowds/ProjectDawn.Navigation.Hybrid/CrowdGroupAuthoring.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Plugins/com.kybernetik.animancer/Runtime/Core/UpdatableListPlayable.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Plugins/com.kybernetik.animancer/Samples/01 Basics/04 Transitions/PlayTransitionOnClick.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Samples/2. MaterialProperties/SampleScenes/2. MaterialProperties/URPMaterialPropertyCutoffAuthoring.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://D:/UnityProject/PersianArts/Hybrid6.2/Assets/Samples/CopyToMainProject/Scripts/ECS/DropResource/Systems/DropPoolingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Samples/CopyToMainProject/Scripts/Pooling/PoolingComponents.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://D:/UnityProject/PersianArts/Hybrid6.2/Assets/Samples/CopyToMainProject/Scripts/Pooling/PoolingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://D:/UnityProject/PersianArts/Hybrid6.2/Assets/Samples/CopyToMainProject/Scripts/Pooling/PoolingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock://D:/UnityProject/PersianArts/Hybrid6.2/Assets/Samples/CopyToMainProject/Scripts/Pooling/PoolingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Samples/CopyToMainProject/Scripts/Ragdoll/ApplyRagdollImpulseSystem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Samples/Rukhanka Animation System 2/2.2.1/Rukhanka Animation Samples/Scripts/PrefabSpawnerSystem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Debug/Editor/DebugLogManagerEditor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Editor/DevBuildTracker.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/General/StateEnums.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Material/DissolveMaterialIDComponentData.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Tools/InGame/SubSceneLoader.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Utility/Editor/PrintStableHashes.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/_ECS/Authoring/EnemyAuthoring.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/_ECS/Components/CollectorModuleComponent.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/_ECS/Systems/Enemy/DissolveTweenToZeroSystem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections/NativeList.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities.graphics@2264b12029b8/Unity.Entities.Graphics/RenderMeshArray.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities.Editor/SystemSchedule/TreeView/SystemTreeViewItem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities.Hybrid/Baking/BakedEntityData.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities.Hybrid/Baking/BakingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities.Hybrid/Baking/BakingUtility.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/ComponentSystemGroup.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/EntityComponentStoreDebug.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/EntityManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/EntityManagerDebug.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Internal/InternalCompilerInterface.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Iterators/EntityQuery.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Iterators/EntityQueryManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Iterators/RefRO.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/ScriptBehaviourUpdateOrder.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Stubs/Unity/Debug.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/SystemBase.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/SystemBaseRegistry.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/SystemState.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/Types/TypeManager.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Entities/WorldUnmanaged.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Scenes.Editor/EditorSubSceneLiveConversionSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Scenes.Editor/EntitySceneBuildUtility.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Scenes.Editor/LiveConversion/LiveConversionConnection.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.entities@e581b903be8e/Unity.Scenes.Editor/LiveConversion/LiveConversionDiffGenerator.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.mathematics@8017b507cc74/Unity.Mathematics/random.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics.Hybrid/Components/PhysicsDebugDisplayAuthoring.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics.Hybrid/Components/PhysicsStepAuthoring.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics/Dynamics/Material/Material.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics/Dynamics/World/PhysicsWorld.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics/ECS/Base/Components/PhysicsComponents.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.physics@b8c0a578fe9d/Unity.Physics/Extensions/PhysicsComponentExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation.crowds/ProjectDawn.Navigation.Hybrid/CrowdSurfaceAuthoring.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation.crowds/ProjectDawn.Navigation/AgentCrowdPath.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation.crowds/ProjectDawn.Navigation/CrowdSurfaceSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation/ProjectDawn.Navigation/Body/AgentBody.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation/ProjectDawn.Navigation/Collider/AgentCollider.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation/ProjectDawn.Navigation/ReciprocalAvoid/AgentReciprocalAvoid.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.projectdawn.navigation/ProjectDawn.Navigation/Separation/AgentSeparation.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.rukhanka.animation/Rukhanka.Hybrid/DebugAndVisualization/RukhankaDebugConfiguration.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.rukhanka.animation/Rukhanka.Hybrid/Rig/FlatHierarchyStripBoneEntitiesBakingSystem.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.rukhanka.animation/Rukhanka.Hybrid/Rig/RigDefinitionAuthoring.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Packages/com.rukhanka.animation/Rukhanka.Runtime/Deformation/Resources/ComputeDeformedVertex.hlsl" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yuipnt4I4qAkGeAHDwwdVMcXBT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;Attach to Unity Player.AndroidPlayer(11,samsung_SM-A055F@************).executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;XThreadsFramesViewSplitterKey&quot;: &quot;0.42523363&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.1.11f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath D:\UnityProject\PersianArts\Hybrid6.2 -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="D:\UnityProject\PersianArts\Hybrid6.2" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.1.11f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath D:\UnityProject\PersianArts\Hybrid6.2 -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="D:\UnityProject\PersianArts\Hybrid6.2" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="AndroidPlayer(11,samsung_SM-A055F@************)" type="UnityPlayer" factoryName="UnityAttachToPlayer" temporary="true">
      <option name="host" value="************" />
      <option name="playerId" value="AndroidPlayer(11,samsung_SM-A055F@************)" />
      <option name="playerInstanceId" value="WithPooling" />
      <option name="port" value="56649" />
      <option name="projectName" value="WithPooling" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Attach to Unity Player.AndroidPlayer(11,samsung_SM-A055F@************)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e6f24aa7-cf7a-424a-9feb-2bf20d415435" name="Changes" comment="" />
      <created>1750691044812</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750691044812</updated>
      <workItem from="1750691045842" duration="646000" />
      <workItem from="1750762569856" duration="2678000" />
      <workItem from="1750841261721" duration="11057000" />
      <workItem from="1750874759681" duration="5051000" />
      <workItem from="1750898030782" duration="5376000" />
      <workItem from="1751017548199" duration="1946000" />
      <workItem from="1751023774600" duration="4557000" />
      <workItem from="1751182313282" duration="3739000" />
      <workItem from="1751521341717" duration="33000" />
      <workItem from="1751674638204" duration="678000" />
      <workItem from="1751736584624" duration="5302000" />
      <workItem from="1751850360945" duration="4515000" />
      <workItem from="1751859032188" duration="1147000" />
      <workItem from="1751939550548" duration="2049000" />
      <workItem from="1752042658488" duration="539000" />
      <workItem from="1752043290279" duration="4980000" />
      <workItem from="1752075240977" duration="11411000" />
      <workItem from="1752092146467" duration="4205000" />
      <workItem from="1752096419399" duration="26800000" />
      <workItem from="1752126132541" duration="2521000" />
      <workItem from="1752129039422" duration="783000" />
      <workItem from="1752129978824" duration="642000" />
      <workItem from="1752178057886" duration="1998000" />
      <workItem from="1752206853314" duration="1149000" />
      <workItem from="1752209717714" duration="9834000" />
      <workItem from="1752250378054" duration="9193000" />
      <workItem from="1752276866819" duration="1926000" />
      <workItem from="1752278958577" duration="16000" />
      <workItem from="1752285820672" duration="6122000" />
      <workItem from="1752325070506" duration="6254000" />
      <workItem from="1752355489077" duration="32342000" />
      <workItem from="1752425440164" duration="1970000" />
      <workItem from="1752436792495" duration="6613000" />
      <workItem from="1752448900687" duration="640000" />
      <workItem from="1752449566077" duration="26565000" />
      <workItem from="1752532870416" duration="8440000" />
      <workItem from="1752613925247" duration="1846000" />
      <workItem from="1752616606414" duration="2415000" />
      <workItem from="1752620042767" duration="3123000" />
      <workItem from="1752640558066" duration="5670000" />
      <workItem from="1752703895441" duration="69000" />
      <workItem from="1752712291316" duration="17434000" />
      <workItem from="1752762179355" duration="4451000" />
      <workItem from="1752797560598" duration="6894000" />
      <workItem from="1752906841325" duration="135000" />
      <workItem from="1752908168194" duration="1267000" />
      <workItem from="1752910253874" duration="1319000" />
      <workItem from="1753256237894" duration="1879000" />
      <workItem from="1753258163807" duration="14079000" />
      <workItem from="1753309106903" duration="5137000" />
      <workItem from="1753352345605" duration="40897000" />
      <workItem from="1753430167822" duration="18817000" />
      <workItem from="1753462253904" duration="14353000" />
      <workItem from="1753506910388" duration="7218000" />
      <workItem from="1753533414036" duration="15358000" />
      <workItem from="1753633094392" duration="25241000" />
      <workItem from="1753692190325" duration="4837000" />
      <workItem from="1753717981877" duration="13379000" />
      <workItem from="1753800193146" duration="10204000" />
      <workItem from="1753826325339" duration="31560000" />
      <workItem from="1753878974009" duration="3221000" />
      <workItem from="1753911157022" duration="19824000" />
      <workItem from="1753964858229" duration="9167000" />
      <workItem from="1753995226425" duration="7594000" />
      <workItem from="1754006185262" duration="16779000" />
      <workItem from="1754505150563" duration="3785000" />
      <workItem from="1754513470091" duration="814000" />
      <workItem from="1754534140963" duration="19159000" />
      <workItem from="1754559897967" duration="552000" />
      <workItem from="1754601166985" duration="9311000" />
      <workItem from="1754634266784" duration="16139000" />
      <workItem from="1754793999443" duration="14284000" />
      <workItem from="1754892545405" duration="6606000" />
      <workItem from="1754919942395" duration="603000" />
      <workItem from="1754950042473" duration="15461000" />
      <workItem from="1754992147675" duration="12031000" />
      <workItem from="1755092405768" duration="2525000" />
      <workItem from="1755098172236" duration="950000" />
      <workItem from="1755099235479" duration="5477000" />
      <workItem from="1755112346887" duration="8540000" />
      <workItem from="1755126416550" duration="953000" />
      <workItem from="1755128266922" duration="3423000" />
      <workItem from="1755161221561" duration="13795000" />
      <workItem from="1755194670923" duration="3370000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/MainOld" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/.." />
      <path value="$PROJECT_DIR$" />
      <path value="$PROJECT_DIR$/../../../" />
    </ignored-roots>
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Scripts/Debug/DebugLogManager.cs</url>
          <line>296</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Scripts\Debug\DebugLogManager.cs" containingFunctionPresentation="Method 'OnLogMessageReceived'">
            <startOffsets>
              <option value="10337" />
            </startOffsets>
            <endOffsets>
              <option value="10366" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Scripts/_ECS/Systems/Weapon/WeaponAimSystem.cs</url>
          <line>49</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Scripts\_ECS\Systems\Weapon\WeaponAimSystem.cs" containingFunctionPresentation="Lambda expression inside Method 'OnUpdate'">
            <startOffsets>
              <option value="1613" />
            </startOffsets>
            <endOffsets>
              <option value="1653" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Scripts/_ECS/Systems/Weapon/WeaponAimSystem.cs</url>
          <line>42</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Scripts\_ECS\Systems\Weapon\WeaponAimSystem.cs" containingFunctionPresentation="Method 'OnUpdate'">
            <startOffsets>
              <option value="1316" />
            </startOffsets>
            <endOffsets>
              <option value="1371" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Scripts/_ECS/Systems/Enemy/EnemyMovementSystem.cs</url>
          <line>91</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Scripts\_ECS\Systems\Enemy\EnemyMovementSystem.cs" containingFunctionPresentation="Method 'Execute'">
            <startOffsets>
              <option value="3501" />
            </startOffsets>
            <endOffsets>
              <option value="3561" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Scripts/_ECS/Systems/Enemy/RagdollSwapSystem.cs</url>
          <line>365</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Scripts\_ECS\Systems\Enemy\RagdollSwapSystem.cs" containingFunctionPresentation="Method 'CompleteRagdollSwap'">
            <startOffsets>
              <option value="16481" />
            </startOffsets>
            <endOffsets>
              <option value="16522" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Samples/CopyToMainProject/Scripts/ECS/DropResource/Systems/DropPoolingSystem.cs</url>
          <line>300</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Samples\CopyToMainProject\Scripts\ECS\DropResource\Systems\DropPoolingSystem.cs" containingFunctionPresentation="Method 'ProcessSingleDropRequest'">
            <startOffsets>
              <option value="14224" />
            </startOffsets>
            <endOffsets>
              <option value="14333" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Samples/CopyToMainProject/Scripts/ECS/DropResource/Systems/DropPoolingSystem.cs</url>
          <line>187</line>
          <properties documentPath="D:\UnityProject\PersianArts\Hybrid6.2\Assets\Samples\CopyToMainProject\Scripts\ECS\DropResource\Systems\DropPoolingSystem.cs" containingFunctionPresentation="Method 'Execute'">
            <startOffsets>
              <option value="8714" />
            </startOffsets>
            <endOffsets>
              <option value="8762" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="68" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="Type#Unity.Entities.DynamicBufferDebugView`1" memberName="Items" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="UNITY_DEBUG_RUN_CONFIGURATION">
        <watch expression="SystemAPI.GetComponent&lt;DeathTimerComponent&gt;(SystemAPI.GetSingletonEntity&lt;DeathTimerComponent&gt;())" language="C#" />
        <watch expression="SystemAPI.GetSingleton&lt;DissolveMaterialID&gt;()" />
      </configuration>
    </watches-manager>
  </component>
</project>