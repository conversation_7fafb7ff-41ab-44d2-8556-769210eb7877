using Unity.Entities;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using Rukhanka.Test.Pooling;
using PlayerFAP.Authorings;

namespace PlayerFAP.Testing
{
    /// <summary>
    /// Test system to validate pool capacity behavior and ensure isolation between different pool types
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(PoolingSystem))]
    public partial struct PoolCapacityTestSystem : ISystem
    {
        private Entity m_PoolManagerEntity;
        private bool m_TestsCompleted;
        private float m_TestStartTime;
        private int m_CurrentTestPhase;
        
        public void OnCreate(ref SystemState state)
        {
            // Find pool manager entity
            var poolManagerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
            if (poolManagerQuery.CalculateEntityCount() > 0)
            {
                m_PoolManagerEntity = poolManagerQuery.GetSingletonEntity();
            }
            
            m_TestsCompleted = false;
            m_TestStartTime = 0f;
            m_CurrentTestPhase = 0;
        }

        public void OnUpdate(ref SystemState state)
        {
            // Only run tests in editor or development builds
#if UNITY_EDITOR || DEVELOPMENT_BUILD
            if (m_TestsCompleted || m_PoolManagerEntity == Entity.Null)
                return;

            float currentTime = (float)SystemAPI.Time.ElapsedTime;
            
            // Start tests after 2 seconds to let systems initialize
            if (currentTime < 2f)
                return;
                
            if (m_TestStartTime == 0f)
            {
                m_TestStartTime = currentTime;
                Debug.Log("[PoolCapacityTest] Starting pool capacity tests...");
            }

            float testElapsed = currentTime - m_TestStartTime;
            
            switch (m_CurrentTestPhase)
            {
                case 0:
                    TestPhase1_FillSinglePoolType(ref state);
                    break;
                case 1:
                    if (testElapsed > 2f) // Wait 2 seconds between phases
                    {
                        TestPhase2_TestOtherTypesStillWork(ref state);
                    }
                    break;
                case 2:
                    if (testElapsed > 4f) // Wait another 2 seconds
                    {
                        TestPhase3_TestSimultaneousCapacityHits(ref state);
                    }
                    break;
                case 3:
                    if (testElapsed > 6f) // Final phase
                    {
                        TestPhase4_ValidateResults(ref state);
                        m_TestsCompleted = true;
                    }
                    break;
            }
#endif
        }

        private void TestPhase1_FillSinglePoolType(ref SystemState state)
        {
            Debug.Log("[PoolCapacityTest] Phase 1: Filling Enemy1 pool to capacity...");
            
            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);
            
            // Try to spawn many Enemy1 entities to fill the pool
            uint enemy1Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy1);
            
            for (int i = 0; i < 50; i++) // Spawn more than typical pool size
            {
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy1Hash,
                    Position = new float3(i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
            }
            
            m_CurrentTestPhase = 1;
        }

        private void TestPhase2_TestOtherTypesStillWork(ref SystemState state)
        {
            Debug.Log("[PoolCapacityTest] Phase 2: Testing that other pool types still work...");
            
            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);
            
            // Try to spawn different enemy types
            uint enemy2Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy2);
            uint enemy3Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy3);
            
            for (int i = 0; i < 10; i++)
            {
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy2Hash,
                    Position = new float3(100f + i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
                
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy3Hash,
                    Position = new float3(200f + i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
            }
            
            m_CurrentTestPhase = 2;
        }

        private void TestPhase3_TestSimultaneousCapacityHits(ref SystemState state)
        {
            Debug.Log("[PoolCapacityTest] Phase 3: Testing simultaneous capacity hits...");
            
            var ecb = SystemAPI.GetSingleton<EndSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(state.WorldUnmanaged);
            
            // Try to fill multiple pool types simultaneously
            uint enemy2Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy2);
            uint enemy3Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy3);
            uint enemy4Hash = PoolingUtility.GetEnumHash(EnemyType.Enemy4);
            
            for (int i = 0; i < 30; i++)
            {
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy2Hash,
                    Position = new float3(300f + i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
                
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy3Hash,
                    Position = new float3(400f + i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
                
                ecb.AppendToBuffer(m_PoolManagerEntity, new PoolSpawnRequest
                {
                    TypeHash = enemy4Hash,
                    Position = new float3(500f + i * 2f, 0f, 0f),
                    Rotation = quaternion.identity,
                    Scale = 1f,
                    RequestingEntity = Entity.Null
                });
            }
            
            m_CurrentTestPhase = 3;
        }

        private void TestPhase4_ValidateResults(ref SystemState state)
        {
            Debug.Log("[PoolCapacityTest] Phase 4: Validating test results...");
            
            // Count entities by type to validate pool behavior
            var enemy1Count = CountEntitiesByType(ref state, EnemyType.Enemy1);
            var enemy2Count = CountEntitiesByType(ref state, EnemyType.Enemy2);
            var enemy3Count = CountEntitiesByType(ref state, EnemyType.Enemy3);
            var enemy4Count = CountEntitiesByType(ref state, EnemyType.Enemy4);
            
            Debug.Log($"[PoolCapacityTest] Final entity counts:");
            Debug.Log($"  Enemy1: {enemy1Count}");
            Debug.Log($"  Enemy2: {enemy2Count}");
            Debug.Log($"  Enemy3: {enemy3Count}");
            Debug.Log($"  Enemy4: {enemy4Count}");
            
            // Validate that other types were able to spawn even when Enemy1 hit capacity
            bool testPassed = enemy2Count > 0 && enemy3Count > 0 && enemy4Count > 0;
            
            if (testPassed)
            {
                Debug.Log("[PoolCapacityTest] ✅ TESTS PASSED: Pool isolation working correctly!");
            }
            else
            {
                Debug.LogError("[PoolCapacityTest] ❌ TESTS FAILED: Pool isolation not working!");
            }
        }

        private int CountEntitiesByType(ref SystemState state, EnemyType enemyType)
        {
            uint typeHash = PoolingUtility.GetEnumHash(enemyType);
            int count = 0;
            
            foreach (var (poolType, entity) in SystemAPI.Query<RefRO<PoolTypeComponent>>()
                         .WithAll<InUse>()
                         .WithEntityAccess())
            {
                if (poolType.ValueRO.TypeHash == typeHash)
                {
                    count++;
                }
            }
            
            return count;
        }
    }
}
