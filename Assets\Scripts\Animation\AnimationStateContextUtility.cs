using System;
using UnityEngine;
using Module.Mono.Animancer.RealsticFemale;
using Mono;

namespace Animation.Core
{
    /// <summary>
    /// Utility class for working with AnimationStateContext
    /// Provides conversion methods and state validation
    /// </summary>
    public static class AnimationStateContextUtility
    {
        #region Context Creation
        
        /// <summary>
        /// Creates AnimationStateContext from current StateManager and CharacterParameters
        /// </summary>
        public static AnimationStateContext CreateFromCurrentState(StateManager stateManager, CharacterParameters parameters)
        {
            if (stateManager == null || parameters == null)
            {
                Debug.LogWarning("[AnimationStateContextUtility] StateManager or CharacterParameters is null, returning default context");
                return AnimationStateContext.Default;
            }
            
            var context = AnimationStateContext.FromCharacterParameters(parameters);
            
            // Update with StateManager data
            context.mainState = stateManager.CurrentMainState;
            
            // Get weapon states from StateManager if available
            try
            {
                if (stateManager.GetSubState(typeof(WeaponSubState)) is WeaponSubState weaponSubState)
                {
                    context.weaponState = weaponSubState;
                }
                
                if (stateManager.GetSubState(typeof(WeaponSubModuleState)) is WeaponSubModuleState weaponModuleState)
                {
                    context.weaponModuleState = weaponModuleState;
                }
            }
            catch (Exception e)
            {
                //Debug.LogWarning($"[AnimationStateContextUtility] Error getting weapon states: {e.Message}");
            }
            
            return context;
        }
        
        /// <summary>
        /// Creates a context for testing purposes with specified states
        /// </summary>
        public static AnimationStateContext CreateTestContext(
            MainState mainState = MainState.Normal,
            MovementSubState movementState = MovementSubState.Standing,
            WeaponSubState weaponState = WeaponSubState.Idle,
            WeaponSubModuleState weaponModuleState = WeaponSubModuleState.EmptyHand,
            bool isAiming = false,
            bool isShooting = false,
            float inputMagnitude = 0f)
        {
            return new AnimationStateContext
            {
                mainState = mainState,
                movementState = movementState,
                weaponState = weaponState,
                weaponModuleState = weaponModuleState,
                isAiming = isAiming,
                isShooting = isShooting,
                inputMagnitude = inputMagnitude,
                isGrounded = true,
                upperBodyWeight = 1f,
                lowerBodyWeight = 1f,
                stateChangeTime = Time.time
            };
        }
        
        #endregion
        
        #region State Validation
        
        /// <summary>
        /// Validates if the animation state context is in a valid state
        /// </summary>
        public static bool IsValidContext(AnimationStateContext context, out string errorMessage)
        {
            errorMessage = string.Empty;
            
            // Check for invalid state combinations
            if (context.weaponModuleState == WeaponSubModuleState.EmptyHand && 
                (context.weaponState == WeaponSubState.Shooting || context.weaponState == WeaponSubState.Reloading))
            {
                errorMessage = "Cannot shoot or reload with empty hands";
                return false;
            }
            
            if (context.isShooting && !context.isAiming && context.weaponModuleState != WeaponSubModuleState.EmptyHand)
            {
                errorMessage = "Cannot shoot without aiming (unless empty handed)";
                return false;
            }
            
            if (context.movementState == MovementSubState.Standing && context.isMoving)
            {
                errorMessage = "Cannot be moving while in Standing state";
                return false;
            }
            
            if (context.inputMagnitude > 0.1f && context.movementState == MovementSubState.Standing)
            {
                errorMessage = "Input magnitude suggests movement but state is Standing";
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// Checks if a state transition is valid
        /// </summary>
        public static bool IsValidTransition(AnimationStateContext from, AnimationStateContext to, out string reason)
        {
            reason = string.Empty;
            
            // Check for invalid movement transitions
            if (from.movementState == MovementSubState.Standing && to.movementState == MovementSubState.WalkingWithTurn)
            {
                reason = "Must go through WalkingStart before WalkingWithTurn";
                return false;
            }
            
            // Check for invalid weapon transitions
            if (from.weaponState == WeaponSubState.Shooting && to.weaponState == WeaponSubState.Reloading)
            {
                reason = "Cannot reload immediately after shooting without going to idle first";
                return false;
            }
            
            // Check for weapon module changes during combat
            if (from.weaponModuleState != to.weaponModuleState && 
                (from.isShooting || from.weaponState == WeaponSubState.Reloading))
            {
                reason = "Cannot change weapon while shooting or reloading";
                return false;
            }
            
            return true;
        }
        
        #endregion
        
        #region State Analysis
        
        /// <summary>
        /// Determines the priority level of a state change
        /// </summary>
        public static TransitionPriority GetTransitionPriority(AnimationStateContext from, AnimationStateContext to)
        {
            // Critical priority for safety states
            if (to.mainState == MainState.Combat && from.mainState != MainState.Combat)
                return TransitionPriority.Critical;
            
            // High priority for weapon state changes
            if (from.weaponState != to.weaponState || from.weaponModuleState != to.weaponModuleState)
                return TransitionPriority.High;
            
            // Medium priority for movement changes
            if (from.movementState != to.movementState)
                return TransitionPriority.Medium;
            
            // Low priority for parameter changes
            if (from.HasMovementChanged(to) || from.HasWeaponChanged(to))
                return TransitionPriority.Low;
            
            return TransitionPriority.None;
        }
        
        /// <summary>
        /// Gets the recommended transition duration based on state change
        /// </summary>
        public static float GetRecommendedTransitionDuration(AnimationStateContext from, AnimationStateContext to)
        {
            var priority = GetTransitionPriority(from, to);
            
            return priority switch
            {
                TransitionPriority.Critical => 0.1f,
                TransitionPriority.High => 0.15f,
                TransitionPriority.Medium => 0.25f,
                TransitionPriority.Low => 0.35f,
                _ => 0.25f
            };
        }
        
        /// <summary>
        /// Determines if the context represents a combat state
        /// </summary>
        public static bool IsCombatState(AnimationStateContext context)
        {
            return context.mainState == MainState.Combat ||
                   context.isAiming ||
                   context.isShooting ||
                   context.weaponState == WeaponSubState.Shooting ||
                   context.weaponState == WeaponSubState.Reloading ||
                   context.weaponModuleState != WeaponSubModuleState.EmptyHand;
        }
        
        /// <summary>
        /// Determines if the context represents an idle state
        /// </summary>
        public static bool IsIdleState(AnimationStateContext context)
        {
            return context.movementState == MovementSubState.Standing &&
                   context.weaponState == WeaponSubState.Idle &&
                   !context.isAiming &&
                   !context.isShooting &&
                   context.inputMagnitude < 0.1f;
        }
        
        #endregion
        
        #region Debug Utilities
        
        /// <summary>
        /// Creates a debug-friendly comparison between two contexts
        /// </summary>
        public static string CompareContexts(AnimationStateContext from, AnimationStateContext to)
        {
            var changes = new System.Text.StringBuilder();
            changes.AppendLine("Animation State Changes:");
            
            if (from.mainState != to.mainState)
                changes.AppendLine($"  MainState: {from.mainState} → {to.mainState}");
            
            if (from.movementState != to.movementState)
                changes.AppendLine($"  MovementState: {from.movementState} → {to.movementState}");
            
            if (from.weaponState != to.weaponState)
                changes.AppendLine($"  WeaponState: {from.weaponState} → {to.weaponState}");
            
            if (from.weaponModuleState != to.weaponModuleState)
                changes.AppendLine($"  WeaponModule: {from.weaponModuleState} → {to.weaponModuleState}");
            
            if (from.isAiming != to.isAiming)
                changes.AppendLine($"  Aiming: {from.isAiming} → {to.isAiming}");
            
            if (from.isShooting != to.isShooting)
                changes.AppendLine($"  Shooting: {from.isShooting} → {to.isShooting}");
            
            if (!Mathf.Approximately(from.inputMagnitude, to.inputMagnitude))
                changes.AppendLine($"  InputMagnitude: {from.inputMagnitude:F2} → {to.inputMagnitude:F2}");
            
            return changes.Length > 25 ? changes.ToString() : "No significant changes detected.";
        }
        
        #endregion
    }
    
    /// <summary>
    /// Priority levels for animation state transitions
    /// </summary>
    public enum TransitionPriority
    {
        None = 0,
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }
}
